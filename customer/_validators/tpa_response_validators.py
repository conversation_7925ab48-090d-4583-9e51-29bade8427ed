"""
TPA Response Validators

This module contains validation classes for <PERSON><PERSON> (Third Party Administrator) API responses.
It provides comprehensive validation for different types of API responses to ensure
data integrity and proper error handling.

Features:
- Token response validation
- Citizen ID search response validation
- Policy list response validation
- Policy detail response validation
- Claims data validation
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class TPAValidationError(Exception):
    """Raised when TPA response validation fails"""
    pass


class TPAResponseValidator:
    """Validator class for TPA API responses"""
    
    @staticmethod
    def validate_token_response(response: Dict[str, Any]) -> bool:
        """
        Validate token response from TPA API
        
        Args:
            response: Response dictionary from GetToken API
            
        Returns:
            True if valid, raises TPAValidationError if invalid
            
        Raises:
            TPAValidationError: If response is invalid
        """
        try:
            # Check if response is a dictionary
            if not isinstance(response, dict):
                raise TPAValidationError("Token response must be a dictionary")
            
            # Check if token field exists
            if 'token' not in response:
                raise TPAValidationError("Token field missing from response")
            
            token = response['token']
            
            # Validate token is string and not empty
            if not isinstance(token, str):
                raise TPAValidationError("Token must be a string")
            
            if not token.strip():
                raise TPAValidationError("Token cannot be empty")
            
            # Token should be reasonable length (basic sanity check)
            if len(token) < 10:
                raise TPAValidationError("Token appears to be too short")
            
            logger.debug("Token response validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Token response validation failed: {str(e)}")
            raise TPAValidationError(f"Token validation failed: {str(e)}")
    
    @staticmethod
    def validate_citizen_response(response: Dict[str, Any]) -> bool:
        """
        Validate citizen ID search response from TPA API
        
        Args:
            response: Response dictionary from SearchCitizenID API
            
        Returns:
            True if valid, raises TPAValidationError if invalid
            
        Raises:
            TPAValidationError: If response is invalid
        """
        try:
            # Check if response is a dictionary
            if not isinstance(response, dict):
                raise TPAValidationError("Citizen response must be a dictionary")
            
            # Check required fields
            required_fields = ['Status', 'CitizenID']
            for field in required_fields:
                if field not in response:
                    raise TPAValidationError(f"Required field '{field}' missing from citizen response")
            
            status = response['Status']
            citizen_id = response['CitizenID']
            
            # Validate status
            if not isinstance(status, str):
                raise TPAValidationError("Status must be a string")
            
            # Status should be "1" for success
            if status != "1":
                logger.warning(f"Citizen ID search returned non-success status: {status}")
                # This is not necessarily an error, just means citizen not found
            
            # Validate CitizenID
            if not isinstance(citizen_id, str):
                raise TPAValidationError("CitizenID must be a string")
            
            # CitizenID should be 13 digits for Thai citizen ID
            if citizen_id and len(citizen_id) != 13:
                logger.warning(f"CitizenID length is not 13 digits: {len(citizen_id)}")
            
            logger.debug("Citizen response validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Citizen response validation failed: {str(e)}")
            raise TPAValidationError(f"Citizen validation failed: {str(e)}")
    
    @staticmethod
    def validate_policy_list_response(response: Dict[str, Any]) -> bool:
        """
        Validate policy list response from TPA API
        
        Args:
            response: Response dictionary from PolicyListSocial API
            
        Returns:
            True if valid, raises TPAValidationError if invalid
            
        Raises:
            TPAValidationError: If response is invalid
        """
        try:
            # Check if response is a dictionary
            if not isinstance(response, dict):
                raise TPAValidationError("Policy list response must be a dictionary")
            
            # Check if ListOfPolicyListSocial exists
            if 'ListOfPolicyListSocial' not in response:
                raise TPAValidationError("ListOfPolicyListSocial field missing from response")
            
            policy_list = response['ListOfPolicyListSocial']
            
            # Validate policy list is a list
            if not isinstance(policy_list, list):
                raise TPAValidationError("ListOfPolicyListSocial must be a list")
            
            # If list is empty, that's valid (customer has no policies)
            if len(policy_list) == 0:
                logger.info("Policy list is empty - customer has no policies")
                return True
            
            # Validate each policy in the list
            for i, policy in enumerate(policy_list):
                if not isinstance(policy, dict):
                    raise TPAValidationError(f"Policy at index {i} must be a dictionary")
                
                # Check for essential policy fields
                essential_fields = ['PolNo']  # Policy number is essential
                for field in essential_fields:
                    if field not in policy:
                        logger.warning(f"Policy at index {i} missing field '{field}'")
            
            logger.debug(f"Policy list response validation passed - {len(policy_list)} policies found")
            return True
            
        except Exception as e:
            logger.error(f"Policy list response validation failed: {str(e)}")
            raise TPAValidationError(f"Policy list validation failed: {str(e)}")
    
    @staticmethod
    def validate_policy_detail_response(response: Dict[str, Any]) -> bool:
        """
        Validate policy detail response from TPA API
        
        Args:
            response: Response dictionary from PolicyDetailSocial API
            
        Returns:
            True if valid, raises TPAValidationError if invalid
            
        Raises:
            TPAValidationError: If response is invalid
        """
        try:
            # Check if response is a dictionary
            if not isinstance(response, dict):
                raise TPAValidationError("Policy detail response must be a dictionary")
            
            # Check if PolicyDetail exists
            if 'PolicyDetail' not in response:
                raise TPAValidationError("PolicyDetail field missing from response")
            
            policy_detail = response['PolicyDetail']
            
            # Validate policy detail is a dictionary
            if not isinstance(policy_detail, dict):
                raise TPAValidationError("PolicyDetail must be a dictionary")
            
            # Validate essential policy detail fields
            essential_fields = ['PolNo', 'CitizenID']
            for field in essential_fields:
                if field not in policy_detail:
                    logger.warning(f"Policy detail missing essential field '{field}'")
            
            logger.debug("Policy detail response validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Policy detail response validation failed: {str(e)}")
            raise TPAValidationError(f"Policy detail validation failed: {str(e)}")
    
    @staticmethod
    def validate_claims_data(claims_data: Union[List[Dict[str, Any]], Dict[str, Any]]) -> bool:
        """
        Validate claims data structure
        
        Args:
            claims_data: Claims data from TPA API (can be list or dict with claims list)
            
        Returns:
            True if valid, raises TPAValidationError if invalid
            
        Raises:
            TPAValidationError: If claims data is invalid
        """
        try:
            # Handle different possible structures
            if isinstance(claims_data, dict):
                if 'claims' in claims_data:
                    claims_list = claims_data['claims']
                else:
                    # Assume the dict itself contains claim fields
                    claims_list = [claims_data]
            elif isinstance(claims_data, list):
                claims_list = claims_data
            else:
                raise TPAValidationError("Claims data must be a list or dictionary")
            
            # Validate claims list
            if not isinstance(claims_list, list):
                raise TPAValidationError("Claims must be a list")
            
            # If empty, that's valid (no claims)
            if len(claims_list) == 0:
                logger.info("Claims list is empty - no claims found")
                return True
            
            # Validate each claim
            for i, claim in enumerate(claims_list):
                if not isinstance(claim, dict):
                    raise TPAValidationError(f"Claim at index {i} must be a dictionary")
                
                # Check for essential claim fields
                essential_fields = ['ClmNo']  # Claim number is essential
                for field in essential_fields:
                    if field not in claim:
                        logger.warning(f"Claim at index {i} missing field '{field}'")
            
            logger.debug(f"Claims data validation passed - {len(claims_list)} claims found")
            return True
            
        except Exception as e:
            logger.error(f"Claims data validation failed: {str(e)}")
            raise TPAValidationError(f"Claims validation failed: {str(e)}")
    
    @staticmethod
    def validate_complete_policy_response(response: Dict[str, Any]) -> bool:
        """
        Validate a complete policy response that includes both policy and claims data
        
        Args:
            response: Complete response with policy and claims data
            
        Returns:
            True if valid, raises TPAValidationError if invalid
            
        Raises:
            TPAValidationError: If response is invalid
        """
        try:
            # Validate basic structure
            if not isinstance(response, dict):
                raise TPAValidationError("Complete policy response must be a dictionary")
            
            # Check for policy data
            if 'policy_data' in response:
                TPAResponseValidator.validate_policy_detail_response({'PolicyDetail': response['policy_data']})
            
            # Check for claims data
            if 'claims_data' in response:
                TPAResponseValidator.validate_claims_data(response['claims_data'])
            
            logger.debug("Complete policy response validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Complete policy response validation failed: {str(e)}")
            raise TPAValidationError(f"Complete policy validation failed: {str(e)}")
