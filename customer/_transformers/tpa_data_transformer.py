"""
TPA Data Transformer

This module contains transformation classes for converting TPA (Third Party Administrator) 
API responses to internal data formats that match the database schema.

Features:
- Transform policy data from external format to internal JSON schema
- Transform claims data from external format to internal JSON schema
- Handle data cleaning and normalization
- Provide fallback values for missing fields
"""

import logging
from typing import Dict, Any, List, Union
from datetime import datetime
from decimal import Decimal, InvalidOperation

logger = logging.getLogger(__name__)


class TPADataTransformationError(Exception):
    """Raised when data transformation fails"""
    pass


class TPADataTransformer:
    """Transformer class for TPA API responses to internal format"""
    
    @staticmethod
    def transform_policy_data(raw_policy_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform external policy API response to internal format
        
        Args:
            raw_policy_data: Raw policy data from TPA API
            
        Returns:
            Transformed policy data matching internal JSON schema
            
        Raises:
            TPADataTransformationError: If transformation fails
        """
        try:
            # Initialize the internal format structure
            transformed_data = {
                "policy_details": {},
                "coverage_details": []
            }
            
            # Transform basic policy details
            policy_details = transformed_data["policy_details"]
            
            # Map fields from external to internal format
            field_mapping = {
                "PolNo": "PolNo",
                "MemberCode": "MemberCode", 
                "Name": "Name",
                "CitizenID": "CitizenID",
                "InsurerName": "InsurerName",
                "CompanyName": "CompanyName",
                "EffFrom": "EffFrom",
                "EffTo": "EffTo",
                "PlanCode": "PlanCode",
                "PlanName": "PlanName"
            }
            
            # Apply field mapping with safe extraction
            for external_field, internal_field in field_mapping.items():
                value = raw_policy_data.get(external_field, "")
                # Clean and normalize the value
                if isinstance(value, str):
                    value = value.strip()
                policy_details[internal_field] = value
            
            # Transform coverage details if present
            coverage_data = raw_policy_data.get("Coverage", [])
            if coverage_data:
                transformed_data["coverage_details"] = TPADataTransformer._transform_coverage_data(coverage_data)
            
            # Handle benefit information if present
            benefits_data = raw_policy_data.get("Benefits", [])
            if benefits_data:
                # Group benefits by MainBenefit
                benefit_groups = TPADataTransformer._group_benefits_by_main_benefit(benefits_data)
                transformed_data["coverage_details"].extend(benefit_groups)
            
            logger.debug(f"Policy data transformation completed for policy: {policy_details.get('PolNo', 'Unknown')}")
            return transformed_data
            
        except Exception as e:
            logger.error(f"Policy data transformation failed: {str(e)}")
            raise TPADataTransformationError(f"Failed to transform policy data: {str(e)}")
    
    @staticmethod
    def _transform_coverage_data(coverage_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Transform coverage data to internal format
        
        Args:
            coverage_data: Raw coverage data from TPA API
            
        Returns:
            Transformed coverage data
        """
        transformed_coverage = []
        
        try:
            for coverage_item in coverage_data:
                if not isinstance(coverage_item, dict):
                    continue
                
                # Extract main benefit information
                main_benefit = coverage_item.get("MainBenefit", "General Coverage")
                
                # Transform individual coverage items
                coverage_list = coverage_item.get("Coverage", [])
                if not isinstance(coverage_list, list):
                    coverage_list = [coverage_list] if coverage_list else []
                
                transformed_coverage_items = []
                for cov_item in coverage_list:
                    if isinstance(cov_item, dict):
                        transformed_item = {
                            "CovNo": str(cov_item.get("CovNo", "")),
                            "CovDesc": str(cov_item.get("CovDesc", "")),
                            "CovLimit": TPADataTransformer._normalize_amount(cov_item.get("CovLimit", "")),
                            "CovUtilized": TPADataTransformer._normalize_amount(cov_item.get("CovUtilized", ""))
                        }
                        transformed_coverage_items.append(transformed_item)
                
                # Add to transformed coverage
                if transformed_coverage_items:
                    transformed_coverage.append({
                        "MainBenefit": main_benefit,
                        "Coverage": transformed_coverage_items
                    })
            
            return transformed_coverage
            
        except Exception as e:
            logger.error(f"Coverage data transformation failed: {str(e)}")
            return []
    
    @staticmethod
    def _group_benefits_by_main_benefit(benefits_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Group benefits by MainBenefit category
        
        Args:
            benefits_data: Raw benefits data from TPA API
            
        Returns:
            Grouped benefits data
        """
        benefit_groups = {}
        
        try:
            for benefit in benefits_data:
                if not isinstance(benefit, dict):
                    continue
                
                main_benefit = benefit.get("MainBenefit", "Other Benefits")
                
                if main_benefit not in benefit_groups:
                    benefit_groups[main_benefit] = {
                        "MainBenefit": main_benefit,
                        "Coverage": []
                    }
                
                # Transform benefit to coverage format
                coverage_item = {
                    "CovNo": str(benefit.get("BenefitCode", "")),
                    "CovDesc": str(benefit.get("BenefitName", "")),
                    "CovLimit": TPADataTransformer._normalize_amount(benefit.get("BenefitLimit", "")),
                    "CovUtilized": TPADataTransformer._normalize_amount(benefit.get("BenefitUtilized", ""))
                }
                
                benefit_groups[main_benefit]["Coverage"].append(coverage_item)
            
            return list(benefit_groups.values())
            
        except Exception as e:
            logger.error(f"Benefits grouping failed: {str(e)}")
            return []
    
    @staticmethod
    def transform_claims_data(raw_claims_data: Union[List[Dict[str, Any]], Dict[str, Any]]) -> Dict[str, Any]:
        """
        Transform external claims API response to internal format
        
        Args:
            raw_claims_data: Raw claims data from TPA API
            
        Returns:
            Transformed claims data matching internal JSON schema
            
        Raises:
            TPADataTransformationError: If transformation fails
        """
        try:
            # Initialize the internal format structure
            transformed_data = {
                "claims": []
            }
            
            # Handle different input formats
            claims_list = []
            if isinstance(raw_claims_data, list):
                claims_list = raw_claims_data
            elif isinstance(raw_claims_data, dict):
                if 'claims' in raw_claims_data:
                    claims_list = raw_claims_data['claims']
                elif 'ClaimList' in raw_claims_data:
                    claims_list = raw_claims_data['ClaimList']
                else:
                    # Assume the dict itself is a single claim
                    claims_list = [raw_claims_data]
            
            # Transform each claim
            for claim_data in claims_list:
                if not isinstance(claim_data, dict):
                    continue
                
                transformed_claim = {
                    "ClmNo": str(claim_data.get("ClmNo", "")),
                    "ClmPolNo": str(claim_data.get("ClmPolNo", "")),
                    "ClmStatus": str(claim_data.get("ClmStatus", "")),
                    "ClmType": str(claim_data.get("ClmType", "")),
                    "ClmDiagTH": str(claim_data.get("ClmDiagTH", "")),
                    "ClmVisitDate": TPADataTransformer._normalize_date(claim_data.get("ClmVisitDate", "")),
                    "ClmIncurredAmt": TPADataTransformer._normalize_amount(claim_data.get("ClmIncurredAmt", "")),
                    "ClmPayable": TPADataTransformer._normalize_amount(claim_data.get("ClmPayable", "")),
                    "ClmProviderTH": str(claim_data.get("ClmProviderTH", ""))
                }
                
                transformed_data["claims"].append(transformed_claim)
            
            logger.debug(f"Claims data transformation completed - {len(transformed_data['claims'])} claims processed")
            return transformed_data
            
        except Exception as e:
            logger.error(f"Claims data transformation failed: {str(e)}")
            raise TPADataTransformationError(f"Failed to transform claims data: {str(e)}")
    
    @staticmethod
    def _normalize_amount(amount_value: Any) -> str:
        """
        Normalize monetary amounts to consistent string format
        
        Args:
            amount_value: Amount value in various formats
            
        Returns:
            Normalized amount as string
        """
        try:
            if amount_value is None or amount_value == "":
                return "0.00"
            
            # Convert to string first
            amount_str = str(amount_value).strip()
            
            # Remove common currency symbols and separators
            amount_str = amount_str.replace(",", "").replace("฿", "").replace("$", "")
            
            # Try to convert to decimal for validation
            try:
                decimal_amount = Decimal(amount_str)
                return f"{decimal_amount:.2f}"
            except (InvalidOperation, ValueError):
                logger.warning(f"Could not normalize amount: {amount_value}")
                return "0.00"
                
        except Exception as e:
            logger.warning(f"Amount normalization failed for {amount_value}: {str(e)}")
            return "0.00"
    
    @staticmethod
    def _normalize_date(date_value: Any) -> str:
        """
        Normalize date values to consistent string format
        
        Args:
            date_value: Date value in various formats
            
        Returns:
            Normalized date as string (YYYY-MM-DD format preferred)
        """
        try:
            if date_value is None or date_value == "":
                return ""
            
            date_str = str(date_value).strip()
            
            # If already in a reasonable format, return as-is
            if len(date_str) >= 8:  # Minimum reasonable date length
                return date_str
            
            return ""
            
        except Exception as e:
            logger.warning(f"Date normalization failed for {date_value}: {str(e)}")
            return ""
    
    @staticmethod
    def combine_policy_and_claims_data(policy_data: Dict[str, Any], claims_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Combine transformed policy and claims data into a single structure
        
        Args:
            policy_data: Transformed policy data
            claims_data: Transformed claims data
            
        Returns:
            Combined data structure
        """
        try:
            combined_data = {
                "policy_details": policy_data.get("policy_details", {}),
                "coverage_details": policy_data.get("coverage_details", []),
                "claims": claims_data.get("claims", []),
                "last_updated": datetime.now().isoformat(),
                "data_source": "TPA_API"
            }
            
            logger.debug("Policy and claims data combination completed")
            return combined_data
            
        except Exception as e:
            logger.error(f"Data combination failed: {str(e)}")
            raise TPADataTransformationError(f"Failed to combine policy and claims data: {str(e)}")
