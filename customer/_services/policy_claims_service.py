"""
Policy Claims Service

This service handles the complete workflow for fetching and storing insurance
policies and claims data from the external TPA API.

Features:
- Validate customer has Citizen ID
- Fetch policies and claims from TPA API
- Transform and validate data
- Store/update data in database
- Handle errors and logging
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ObjectDoesNotExist

from customer.models import Customer, CustomerPolicies
from customer._services.tpa_api_client import TPAAPIClient, TPAResponse
from customer._validators.tpa_response_validators import TPAResponseValidator, TPAValidationError
from customer._transformers.tpa_data_transformer import TPADataTransformer, TPADataTransformationError
from user.models import User

logger = logging.getLogger(__name__)


class PolicyClaimsServiceError(Exception):
    """Base exception for PolicyClaimsService errors"""
    pass


class PolicyClaimsService:
    """Service for managing policy and claims data fetching and storage"""
    
    def __init__(self):
        self.tpa_client = TPAAPIClient()
        self.validator = TPAResponseValidator()
        self.transformer = TPADataTransformer()
    
    def fetch_and_store_policies(self, customer_id: int, user_id: int) -> Dict[str, Any]:
        """
        Complete workflow to fetch policies and claims from TPA API and store in database
        
        Args:
            customer_id: ID of the customer to fetch policies for
            user_id: ID of the user performing the operation
            
        Returns:
            Dictionary with operation results and data
            
        Raises:
            PolicyClaimsServiceError: If operation fails
        """
        try:
            logger.info(f"Starting policy fetch workflow for customer {customer_id}")
            
            # Step 1: Validate customer has Citizen ID
            customer, citizen_id = self._validate_customer_citizen_id(customer_id)
            
            # Step 2: Get user for audit trail
            user = self._get_user(user_id)
            
            # Step 3: Get social_id and channel_id for TPA authentication
            # For now, using default values - in production, these should come from customer's platform identity
            social_id = f"customer_{customer_id}"  # Placeholder
            channel_id = "LINE"  # Default channel
            
            # Step 4: Verify Citizen ID with TPA
            citizen_verification = self._verify_citizen_id_with_tpa(citizen_id, social_id, channel_id)
            if not citizen_verification:
                raise PolicyClaimsServiceError("Citizen ID verification failed with TPA")
            
            # Step 5: Fetch policy list from TPA
            policy_list = self._fetch_policy_list_from_tpa(citizen_id, social_id, channel_id)
            
            # Step 6: For each policy, fetch details and claims
            policies_processed = []
            total_claims = 0
            
            for policy_summary in policy_list:
                policy_no = policy_summary.get('PolNo')
                if not policy_no:
                    logger.warning(f"Policy without PolNo found, skipping: {policy_summary}")
                    continue
                
                try:
                    # Fetch detailed policy information
                    policy_detail = self._fetch_policy_detail_from_tpa(policy_no, social_id, channel_id)
                    
                    # Transform policy data
                    transformed_policy = self.transformer.transform_policy_data(policy_detail)
                    
                    # For now, we'll use empty claims data since the TPA API structure for claims is not clear
                    # In a real implementation, you would fetch claims data here
                    transformed_claims = {"claims": []}
                    
                    # Step 7: Update or create policy record
                    policy_record = self.update_or_create_policy_record(
                        customer_id=customer_id,
                        policy_no=policy_no,
                        policy_data=transformed_policy,
                        claims_data=transformed_claims,
                        user_id=user_id
                    )
                    
                    policies_processed.append({
                        'policy_no': policy_no,
                        'record_id': policy_record.id,
                        'claims_count': len(transformed_claims.get('claims', []))
                    })
                    
                    total_claims += len(transformed_claims.get('claims', []))
                    
                except Exception as e:
                    logger.error(f"Failed to process policy {policy_no}: {str(e)}")
                    # Continue with other policies
                    continue
            
            # Step 8: Prepare response
            response_data = {
                'success': True,
                'data': {
                    'customer_id': customer_id,
                    'policies_count': len(policies_processed),
                    'claims_count': total_claims,
                    'policies': policies_processed,
                    'last_updated': timezone.now().isoformat()
                },
                'message': f'Successfully fetched and stored {len(policies_processed)} policies with {total_claims} claims'
            }
            
            logger.info(f"Policy fetch workflow completed for customer {customer_id}: {len(policies_processed)} policies processed")
            return response_data
            
        except Exception as e:
            logger.error(f"Policy fetch workflow failed for customer {customer_id}: {str(e)}")
            raise PolicyClaimsServiceError(f"Failed to fetch and store policies: {str(e)}")
    
    def _validate_customer_citizen_id(self, customer_id: int) -> Tuple[Customer, str]:
        """
        Validate that customer exists and has a Citizen ID
        
        Args:
            customer_id: ID of the customer
            
        Returns:
            Tuple of (Customer object, citizen_id string)
            
        Raises:
            PolicyClaimsServiceError: If customer not found or no citizen ID
        """
        try:
            customer = Customer.objects.get(customer_id=customer_id)
        except ObjectDoesNotExist:
            raise PolicyClaimsServiceError(f"Customer with ID {customer_id} not found")
        
        if not customer.national_id:
            raise PolicyClaimsServiceError(f"Customer {customer_id} does not have a Citizen ID")
        
        # Validate citizen ID format (should be 13 digits for Thai ID)
        citizen_id = customer.national_id.strip()
        if len(citizen_id) != 13 or not citizen_id.isdigit():
            raise PolicyClaimsServiceError(f"Invalid Citizen ID format for customer {customer_id}")
        
        return customer, citizen_id
    
    def _get_user(self, user_id: int) -> User:
        """
        Get user object for audit trail
        
        Args:
            user_id: ID of the user
            
        Returns:
            User object
            
        Raises:
            PolicyClaimsServiceError: If user not found
        """
        try:
            return User.objects.get(id=user_id)
        except ObjectDoesNotExist:
            raise PolicyClaimsServiceError(f"User with ID {user_id} not found")
    
    def _verify_citizen_id_with_tpa(self, citizen_id: str, social_id: str, channel_id: str) -> bool:
        """
        Verify citizen ID exists in TPA system
        
        Args:
            citizen_id: Thai citizen ID
            social_id: Social ID for authentication
            channel_id: Channel ID for authentication
            
        Returns:
            True if citizen ID is valid, False otherwise
        """
        try:
            response = self.tpa_client.search_citizen_id(citizen_id, social_id, channel_id)
            
            if not response.success:
                logger.warning(f"TPA citizen verification failed: {response.error_message}")
                return False
            
            # Validate response structure
            self.validator.validate_citizen_response(response.data)
            
            # Check if citizen was found (Status = "1")
            status = response.data.get('Status')
            if status == "1":
                logger.info(f"Citizen ID {citizen_id} verified successfully with TPA")
                return True
            else:
                logger.warning(f"Citizen ID {citizen_id} not found in TPA system (Status: {status})")
                return False
                
        except (TPAValidationError, Exception) as e:
            logger.error(f"Citizen ID verification error: {str(e)}")
            return False
    
    def _fetch_policy_list_from_tpa(self, citizen_id: str, social_id: str, channel_id: str) -> List[Dict[str, Any]]:
        """
        Fetch list of policies from TPA API
        
        Args:
            citizen_id: Thai citizen ID
            social_id: Social ID for authentication
            channel_id: Channel ID for authentication
            
        Returns:
            List of policy summaries
            
        Raises:
            PolicyClaimsServiceError: If fetch fails
        """
        try:
            response = self.tpa_client.get_policy_list(citizen_id, social_id, channel_id)
            
            if not response.success:
                raise PolicyClaimsServiceError(f"Failed to fetch policy list: {response.error_message}")
            
            # Validate response structure
            self.validator.validate_policy_list_response(response.data)
            
            policy_list = response.data.get('ListOfPolicyListSocial', [])
            logger.info(f"Fetched {len(policy_list)} policies from TPA for citizen {citizen_id}")
            
            return policy_list
            
        except (TPAValidationError, Exception) as e:
            logger.error(f"Policy list fetch error: {str(e)}")
            raise PolicyClaimsServiceError(f"Failed to fetch policy list: {str(e)}")
    
    def _fetch_policy_detail_from_tpa(self, policy_no: str, social_id: str, channel_id: str) -> Dict[str, Any]:
        """
        Fetch detailed policy information from TPA API
        
        Args:
            policy_no: Policy number
            social_id: Social ID for authentication
            channel_id: Channel ID for authentication
            
        Returns:
            Policy detail data
            
        Raises:
            PolicyClaimsServiceError: If fetch fails
        """
        try:
            response = self.tpa_client.get_policy_detail(policy_no, social_id, channel_id)
            
            if not response.success:
                raise PolicyClaimsServiceError(f"Failed to fetch policy detail: {response.error_message}")
            
            # Validate response structure
            self.validator.validate_policy_detail_response(response.data)
            
            policy_detail = response.data.get('PolicyDetail', {})
            logger.debug(f"Fetched policy detail for policy {policy_no}")
            
            return policy_detail
            
        except (TPAValidationError, Exception) as e:
            logger.error(f"Policy detail fetch error for {policy_no}: {str(e)}")
            raise PolicyClaimsServiceError(f"Failed to fetch policy detail for {policy_no}: {str(e)}")

    def update_or_create_policy_record(
        self,
        customer_id: int,
        policy_no: str,
        policy_data: Dict[str, Any],
        claims_data: Dict[str, Any],
        user_id: int
    ) -> CustomerPolicies:
        """
        Update existing policy record or create new one

        Args:
            customer_id: ID of the customer
            policy_no: Policy number
            policy_data: Transformed policy data
            claims_data: Transformed claims data
            user_id: ID of the user performing the operation

        Returns:
            CustomerPolicies record

        Raises:
            PolicyClaimsServiceError: If operation fails
        """
        try:
            with transaction.atomic():
                customer = Customer.objects.get(customer_id=customer_id)
                user = User.objects.get(id=user_id)

                # Try to get existing record
                try:
                    policy_record = CustomerPolicies.objects.get(
                        customer=customer,
                        policy_no=policy_no
                    )

                    # Check if data has changed (simple comparison)
                    data_changed = (
                        policy_record.policies != policy_data or
                        policy_record.claims != claims_data
                    )

                    if data_changed:
                        # Update existing record
                        policy_record.policies = policy_data
                        policy_record.claims = claims_data
                        policy_record.updated_by = user
                        policy_record.save()

                        logger.info(f"Updated existing policy record for customer {customer_id}, policy {policy_no}")
                    else:
                        logger.info(f"No changes detected for policy {policy_no}, skipping update")

                except CustomerPolicies.DoesNotExist:
                    # Create new record
                    policy_record = CustomerPolicies.objects.create(
                        customer=customer,
                        policy_no=policy_no,
                        policies=policy_data,
                        claims=claims_data,
                        updated_by=user
                    )

                    logger.info(f"Created new policy record for customer {customer_id}, policy {policy_no}")

                return policy_record

        except Exception as e:
            logger.error(f"Failed to update/create policy record: {str(e)}")
            raise PolicyClaimsServiceError(f"Database operation failed: {str(e)}")

    def check_customer_has_citizen_id(self, customer_id: int) -> Dict[str, Any]:
        """
        Check if customer has a valid Citizen ID

        Args:
            customer_id: ID of the customer to check

        Returns:
            Dictionary with check results
        """
        try:
            customer = Customer.objects.get(customer_id=customer_id)

            has_citizen_id = bool(customer.national_id and customer.national_id.strip())

            # Additional validation for Thai citizen ID format
            valid_format = False
            if has_citizen_id:
                citizen_id = customer.national_id.strip()
                valid_format = len(citizen_id) == 13 and citizen_id.isdigit()

            return {
                'customer_id': customer_id,
                'has_citizen_id': has_citizen_id and valid_format,
                'citizen_id_format_valid': valid_format,
                'message': 'Citizen ID check completed'
            }

        except ObjectDoesNotExist:
            return {
                'customer_id': customer_id,
                'has_citizen_id': False,
                'citizen_id_format_valid': False,
                'message': f'Customer {customer_id} not found'
            }
        except Exception as e:
            logger.error(f"Error checking citizen ID for customer {customer_id}: {str(e)}")
            return {
                'customer_id': customer_id,
                'has_citizen_id': False,
                'citizen_id_format_valid': False,
                'message': f'Error checking citizen ID: {str(e)}'
            }

    def get_stored_policies_and_claims(self, customer_id: int) -> Dict[str, Any]:
        """
        Get stored policies and claims data for a customer

        Args:
            customer_id: ID of the customer

        Returns:
            Dictionary with stored policies and claims data
        """
        try:
            customer = Customer.objects.get(customer_id=customer_id)

            # Get all policy records for this customer
            policy_records = CustomerPolicies.objects.filter(
                customer=customer
            ).order_by('-updated_on')

            policies_data = []
            total_claims = 0

            for record in policy_records:
                policy_info = {
                    'id': record.id,
                    'policy_no': record.policy_no,
                    'policy_details': record.get_policy_details(),
                    'coverage_details': record.get_coverage_details(),
                    'claims': record.get_claims_list(),
                    'claims_count': record.get_claims_count(),
                    'last_updated': record.updated_on.isoformat(),
                    'updated_by': record.updated_by.username if record.updated_by else None
                }

                policies_data.append(policy_info)
                total_claims += record.get_claims_count()

            return {
                'customer_id': customer_id,
                'customer_name': customer.get_full_name() or customer.name,
                'policies_count': len(policies_data),
                'total_claims': total_claims,
                'policies': policies_data,
                'last_fetch_time': policy_records.first().updated_on.isoformat() if policy_records else None
            }

        except ObjectDoesNotExist:
            return {
                'customer_id': customer_id,
                'customer_name': None,
                'policies_count': 0,
                'total_claims': 0,
                'policies': [],
                'last_fetch_time': None,
                'message': f'Customer {customer_id} not found'
            }
        except Exception as e:
            logger.error(f"Error retrieving stored policies for customer {customer_id}: {str(e)}")
            raise PolicyClaimsServiceError(f"Failed to retrieve stored policies: {str(e)}")
