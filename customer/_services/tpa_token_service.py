"""
TPA Token Management Service

This service handles authentication token management for the TPA (Third Party Administrator) API.
It provides token caching, automatic refresh, and secure token storage using Redis.

Features:
- Redis-based token caching with configurable expiry
- Automatic token refresh before expiration
- Thread-safe token operations
- Comprehensive error handling and logging
"""

import json
import logging
import time
from typing import Dict, Optional, Tuple
from datetime import datetime, timedelta

import redis
import requests
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

from customer._config.tpa_api_config import TPAAPIConfig

logger = logging.getLogger(__name__)


class TPATokenService:
    """Service for managing TPA API authentication tokens with Redis caching"""
    
    def __init__(self):
        """Initialize the token service with Redis connection"""
        try:
            self.redis_client = redis.Redis(
                host=getattr(settings, 'REDIS_HOST', 'redis'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=getattr(settings, 'REDIS_TPA_TOKEN_DB', 4),  # Use separate DB for TPA tokens
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            # Test connection
            self.redis_client.ping()
            logger.info("Redis connection for TPA token service established")
        except redis.ConnectionError as e:
            logger.error(f"Failed to connect to Redis for TPA token service: {str(e)}")
            raise ImproperlyConfigured("Redis connection failed for TPA token service")
        
        # Load configuration
        self.config = TPAAPIConfig()
        self.token_expiry = self.config.TOKEN_SETTINGS['expiry_seconds']
        self.refresh_threshold = self.config.TOKEN_SETTINGS['refresh_threshold_seconds']
        self.cache_key_prefix = self.config.TOKEN_SETTINGS['cache_key_prefix']
    
    def _get_token_cache_key(self, social_id: str, channel_id: str) -> str:
        """
        Generate Redis cache key for token storage
        
        Args:
            social_id: Social ID for the token request
            channel_id: Channel ID for the token request
            
        Returns:
            Redis cache key string
        """
        return f"{self.cache_key_prefix}:{social_id}:{channel_id}"
    
    def _get_token_metadata_key(self, social_id: str, channel_id: str) -> str:
        """
        Generate Redis cache key for token metadata storage
        
        Args:
            social_id: Social ID for the token request
            channel_id: Channel ID for the token request
            
        Returns:
            Redis cache key string for metadata
        """
        return f"{self.cache_key_prefix}_meta:{social_id}:{channel_id}"
    
    def _call_token_api(self, social_id: str, channel_id: str) -> Tuple[str, bool]:
        """
        Call the external TPA API to get a new token
        
        Args:
            social_id: Social ID for the token request
            channel_id: Channel ID for the token request
            
        Returns:
            Tuple of (token_string, success_boolean)
            
        Raises:
            requests.RequestException: If API call fails
        """
        url = self.config.get_full_url('get_token')
        
        payload = {
            "SocialID": social_id,
            "ChannelID": channel_id,
            "Username": self.config.CREDENTIALS['username'],
            "Password": self.config.CREDENTIALS['password'],
            "Channel": self.config.CREDENTIALS['channel']
        }
        
        headers = self.config.DEFAULT_HEADERS.copy()
        
        try:
            logger.info(f"Requesting new TPA token for social_id: {social_id}, channel_id: {channel_id}")
            
            response = requests.post(
                url,
                json=payload,
                headers=headers,
                timeout=(
                    self.config.TIMEOUTS['connect'],
                    self.config.TIMEOUTS['read']
                ),
                verify=self.config.VALIDATION_SETTINGS['validate_ssl']
            )
            
            response.raise_for_status()
            
            # Parse response
            response_data = response.json()
            
            # Validate response structure
            if 'token' not in response_data:
                logger.error(f"Invalid token response structure: {response_data}")
                return None, False
            
            token = response_data['token']
            if not token or not isinstance(token, str):
                logger.error(f"Invalid token value in response: {token}")
                return None, False
            
            logger.info(f"Successfully obtained TPA token for social_id: {social_id}")
            return token, True
            
        except requests.exceptions.Timeout:
            logger.error(f"Timeout while requesting TPA token for social_id: {social_id}")
            return None, False
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed while getting TPA token for social_id: {social_id}: {str(e)}")
            return None, False
        except (ValueError, KeyError) as e:
            logger.error(f"Invalid response format from TPA token API: {str(e)}")
            return None, False
    
    def _store_token_in_cache(self, social_id: str, channel_id: str, token: str) -> bool:
        """
        Store token and metadata in Redis cache
        
        Args:
            social_id: Social ID for the token
            channel_id: Channel ID for the token
            token: Token string to store
            
        Returns:
            True if storage was successful, False otherwise
        """
        try:
            token_key = self._get_token_cache_key(social_id, channel_id)
            metadata_key = self._get_token_metadata_key(social_id, channel_id)
            
            # Store token with expiry
            self.redis_client.setex(token_key, self.token_expiry, token)
            
            # Store metadata
            metadata = {
                'created_at': datetime.now().isoformat(),
                'expires_at': (datetime.now() + timedelta(seconds=self.token_expiry)).isoformat(),
                'social_id': social_id,
                'channel_id': channel_id
            }
            self.redis_client.setex(metadata_key, self.token_expiry, json.dumps(metadata))
            
            logger.debug(f"Token stored in cache for social_id: {social_id}, expires in {self.token_expiry} seconds")
            return True
            
        except redis.RedisError as e:
            logger.error(f"Failed to store token in cache: {str(e)}")
            return False
    
    def _get_token_from_cache(self, social_id: str, channel_id: str) -> Optional[str]:
        """
        Retrieve token from Redis cache
        
        Args:
            social_id: Social ID for the token
            channel_id: Channel ID for the token
            
        Returns:
            Token string if found and valid, None otherwise
        """
        try:
            token_key = self._get_token_cache_key(social_id, channel_id)
            token = self.redis_client.get(token_key)
            
            if token:
                # Check if token is close to expiry
                ttl = self.redis_client.ttl(token_key)
                if ttl > self.refresh_threshold:
                    logger.debug(f"Retrieved valid token from cache for social_id: {social_id}")
                    return token
                else:
                    logger.info(f"Token for social_id: {social_id} is close to expiry (TTL: {ttl}s), will refresh")
                    return None
            
            return None
            
        except redis.RedisError as e:
            logger.error(f"Failed to retrieve token from cache: {str(e)}")
            return None
    
    def get_or_create_token(self, social_id: str, channel_id: str) -> Optional[str]:
        """
        Get existing token from cache or create a new one
        
        Args:
            social_id: Social ID for the token request
            channel_id: Channel ID for the token request
            
        Returns:
            Valid token string, or None if unable to obtain token
        """
        if not social_id or not channel_id:
            logger.error("social_id and channel_id are required for token generation")
            return None
        
        # Try to get token from cache first
        cached_token = self._get_token_from_cache(social_id, channel_id)
        if cached_token:
            return cached_token
        
        # Token not in cache or expired, get new one
        logger.info(f"No valid cached token found, requesting new token for social_id: {social_id}")
        
        token, success = self._call_token_api(social_id, channel_id)
        if success and token:
            # Store in cache
            if self._store_token_in_cache(social_id, channel_id, token):
                return token
            else:
                # Even if caching fails, return the token
                logger.warning("Token caching failed, but returning token anyway")
                return token
        
        logger.error(f"Failed to obtain TPA token for social_id: {social_id}")
        return None
    
    def invalidate_token(self, social_id: str, channel_id: str) -> bool:
        """
        Invalidate (remove) token from cache
        
        Args:
            social_id: Social ID for the token
            channel_id: Channel ID for the token
            
        Returns:
            True if token was removed, False otherwise
        """
        try:
            token_key = self._get_token_cache_key(social_id, channel_id)
            metadata_key = self._get_token_metadata_key(social_id, channel_id)
            
            # Remove both token and metadata
            deleted_count = self.redis_client.delete(token_key, metadata_key)
            
            if deleted_count > 0:
                logger.info(f"Invalidated token for social_id: {social_id}")
                return True
            else:
                logger.warning(f"No token found to invalidate for social_id: {social_id}")
                return False
                
        except redis.RedisError as e:
            logger.error(f"Failed to invalidate token: {str(e)}")
            return False
    
    def get_token_metadata(self, social_id: str, channel_id: str) -> Optional[Dict]:
        """
        Get token metadata from cache
        
        Args:
            social_id: Social ID for the token
            channel_id: Channel ID for the token
            
        Returns:
            Token metadata dictionary, or None if not found
        """
        try:
            metadata_key = self._get_token_metadata_key(social_id, channel_id)
            metadata_json = self.redis_client.get(metadata_key)
            
            if metadata_json:
                return json.loads(metadata_json)
            
            return None
            
        except (redis.RedisError, json.JSONDecodeError) as e:
            logger.error(f"Failed to retrieve token metadata: {str(e)}")
            return None
    
    def cleanup_expired_tokens(self) -> int:
        """
        Clean up expired tokens from cache (maintenance operation)
        
        Returns:
            Number of tokens cleaned up
        """
        try:
            pattern = f"{self.cache_key_prefix}:*"
            keys = self.redis_client.keys(pattern)
            
            cleaned_count = 0
            for key in keys:
                ttl = self.redis_client.ttl(key)
                if ttl == -2:  # Key doesn't exist (expired)
                    cleaned_count += 1
            
            logger.info(f"Cleaned up {cleaned_count} expired tokens")
            return cleaned_count
            
        except redis.RedisError as e:
            logger.error(f"Failed to cleanup expired tokens: {str(e)}")
            return 0
