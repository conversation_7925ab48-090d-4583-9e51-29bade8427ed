"""
TPA API Client Service

This service provides a comprehensive client for interacting with the TPA (Third Party Administrator) API.
It handles authentication, request/response processing, error handling, and data validation.

Features:
- Automatic token management and refresh
- Comprehensive error handling and retry logic
- Request/response validation
- Structured logging for debugging and monitoring
- Type-safe API interactions
"""

import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from django.core.exceptions import ValidationError

from customer._config.tpa_api_config import TPAAPIConfig
from customer._services.tpa_token_service import TPATokenService

logger = logging.getLogger(__name__)


class TPAAPIError(Exception):
    """Base exception for TPA API errors"""
    pass


class TPAAuthenticationError(TPAAPIError):
    """Raised when authentication fails"""
    pass


class TPAValidationError(TPAAPIError):
    """Raised when API response validation fails"""
    pass


class TPANetworkError(TPAAPIError):
    """Raised when network-related errors occur"""
    pass


@dataclass
class TPAResponse:
    """Structured response from TPA API"""
    success: bool
    data: Optional[Dict[str, Any]]
    error_message: Optional[str]
    status_code: Optional[int]
    response_time: float


class TPAAPIClient:
    """Client for interacting with TPA API"""
    
    def __init__(self):
        """Initialize the TPA API client"""
        self.config = TPAAPIConfig()
        self.token_service = TPATokenService()
        
        # Configure requests session with retry strategy
        self.session = requests.Session()
        
        # Set up retry strategy
        retry_strategy = Retry(
            total=self.config.RETRY_SETTINGS['max_retries'],
            backoff_factor=self.config.RETRY_SETTINGS['backoff_factor'],
            status_forcelist=self.config.RETRY_SETTINGS['retry_on_status'],
            allowed_methods=["GET", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Set default headers
        self.session.headers.update(self.config.DEFAULT_HEADERS)
        
        logger.info("TPA API Client initialized")
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                     headers: Optional[Dict] = None) -> TPAResponse:
        """
        Make HTTP request to TPA API
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint key from config
            data: Request payload
            headers: Additional headers
            
        Returns:
            TPAResponse object with structured response data
        """
        start_time = time.time()
        
        try:
            url = self.config.get_full_url(endpoint)
            
            # Merge headers
            request_headers = self.session.headers.copy()
            if headers:
                request_headers.update(headers)
            
            logger.debug(f"Making {method} request to {url}")
            
            response = self.session.request(
                method=method,
                url=url,
                json=data,
                headers=request_headers,
                timeout=(
                    self.config.TIMEOUTS['connect'],
                    self.config.TIMEOUTS['read']
                ),
                verify=self.config.VALIDATION_SETTINGS['validate_ssl']
            )
            
            response_time = time.time() - start_time
            
            # Check response size
            if len(response.content) > self.config.VALIDATION_SETTINGS['max_response_size']:
                raise TPAValidationError(f"Response size exceeds maximum allowed: {len(response.content)} bytes")
            
            # Parse JSON response
            try:
                response_data = response.json()
            except ValueError as e:
                logger.error(f"Invalid JSON response from TPA API: {str(e)}")
                return TPAResponse(
                    success=False,
                    data=None,
                    error_message=f"Invalid JSON response: {str(e)}",
                    status_code=response.status_code,
                    response_time=response_time
                )
            
            # Check HTTP status
            if response.status_code >= 400:
                error_msg = f"HTTP {response.status_code}: {response_data.get('message', 'Unknown error')}"
                logger.error(f"TPA API error: {error_msg}")
                return TPAResponse(
                    success=False,
                    data=response_data,
                    error_message=error_msg,
                    status_code=response.status_code,
                    response_time=response_time
                )
            
            logger.debug(f"TPA API request successful in {response_time:.2f}s")
            return TPAResponse(
                success=True,
                data=response_data,
                error_message=None,
                status_code=response.status_code,
                response_time=response_time
            )
            
        except requests.exceptions.Timeout:
            response_time = time.time() - start_time
            error_msg = f"Request timeout after {response_time:.2f}s"
            logger.error(error_msg)
            return TPAResponse(
                success=False,
                data=None,
                error_message=error_msg,
                status_code=None,
                response_time=response_time
            )
            
        except requests.exceptions.RequestException as e:
            response_time = time.time() - start_time
            error_msg = f"Network error: {str(e)}"
            logger.error(error_msg)
            return TPAResponse(
                success=False,
                data=None,
                error_message=error_msg,
                status_code=None,
                response_time=response_time
            )
    
    def _validate_response_structure(self, response_data: Dict, endpoint: str) -> bool:
        """
        Validate response structure based on endpoint requirements
        
        Args:
            response_data: Response data to validate
            endpoint: Endpoint key for validation rules
            
        Returns:
            True if valid, False otherwise
        """
        required_fields = self.config.VALIDATION_SETTINGS['required_response_fields'].get(endpoint, [])
        
        for field in required_fields:
            if field not in response_data:
                logger.error(f"Missing required field '{field}' in {endpoint} response")
                return False
        
        return True
    
    def search_citizen_id(self, citizen_id: str, social_id: str, channel_id: str) -> TPAResponse:
        """
        Search for citizen ID in TPA system
        
        Args:
            citizen_id: Thai citizen ID to search for
            social_id: Social ID for authentication
            channel_id: Channel ID for authentication
            
        Returns:
            TPAResponse with citizen search results
        """
        # Get authentication token
        token = self.token_service.get_or_create_token(social_id, channel_id)
        if not token:
            return TPAResponse(
                success=False,
                data=None,
                error_message="Failed to obtain authentication token",
                status_code=None,
                response_time=0.0
            )
        
        # Prepare request data
        payload = {
            "CitizenID": citizen_id,
            "Token": token
        }
        
        # Make request
        response = self._make_request("POST", "search_citizen", payload)
        
        # Validate response structure
        if response.success and response.data:
            if not self._validate_response_structure(response.data, "search_citizen"):
                response.success = False
                response.error_message = "Invalid response structure"
        
        return response
    
    def get_policy_list(self, citizen_id: str, social_id: str, channel_id: str) -> TPAResponse:
        """
        Get list of policies for a citizen
        
        Args:
            citizen_id: Thai citizen ID
            social_id: Social ID for authentication
            channel_id: Channel ID for authentication
            
        Returns:
            TPAResponse with policy list
        """
        # Get authentication token
        token = self.token_service.get_or_create_token(social_id, channel_id)
        if not token:
            return TPAResponse(
                success=False,
                data=None,
                error_message="Failed to obtain authentication token",
                status_code=None,
                response_time=0.0
            )
        
        # Prepare request data
        payload = {
            "CitizenID": citizen_id,
            "Token": token
        }
        
        # Make request
        response = self._make_request("POST", "policy_list", payload)
        
        # Validate response structure
        if response.success and response.data:
            if not self._validate_response_structure(response.data, "policy_list"):
                response.success = False
                response.error_message = "Invalid response structure"
        
        return response
    
    def get_policy_detail(self, policy_no: str, social_id: str, channel_id: str) -> TPAResponse:
        """
        Get detailed information for a specific policy
        
        Args:
            policy_no: Policy number to get details for
            social_id: Social ID for authentication
            channel_id: Channel ID for authentication
            
        Returns:
            TPAResponse with policy details
        """
        # Get authentication token
        token = self.token_service.get_or_create_token(social_id, channel_id)
        if not token:
            return TPAResponse(
                success=False,
                data=None,
                error_message="Failed to obtain authentication token",
                status_code=None,
                response_time=0.0
            )
        
        # Prepare request data
        payload = {
            "PolNo": policy_no,
            "Token": token
        }
        
        # Make request
        response = self._make_request("POST", "policy_detail", payload)
        
        # Validate response structure
        if response.success and response.data:
            if not self._validate_response_structure(response.data, "policy_detail"):
                response.success = False
                response.error_message = "Invalid response structure"
        
        return response
    
    def health_check(self) -> bool:
        """
        Perform a basic health check of the TPA API
        
        Returns:
            True if API is accessible, False otherwise
        """
        try:
            # Try to make a simple request to check connectivity
            url = self.config.BASE_URL
            response = requests.get(
                url,
                timeout=self.config.TIMEOUTS['connect'],
                verify=self.config.VALIDATION_SETTINGS['validate_ssl']
            )
            return response.status_code < 500
        except Exception as e:
            logger.error(f"TPA API health check failed: {str(e)}")
            return False
