"""
T<PERSON> (Third Party Administrator) API Configuration

This module contains configuration settings for integrating with the external
TPA API for fetching insurance policies and claims data.

Configuration includes:
- API endpoints
- Authentication credentials
- Timeout settings
- Request/response format specifications
"""

import os
from typing import Dict, Any


class TPAAPIConfig:
    """Configuration class for TPA API integration"""
    
    # Base URL for TPA API
    BASE_URL = "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api"
    
    # API Endpoints
    ENDPOINTS = {
        "get_token": "/GetToken",
        "search_citizen": "/SearchCitizenID",
        "policy_list": "/PolicyListSocial",
        "policy_detail": "/PolicyDetailSocial"
    }
    
    # Authentication credentials
    # Note: In production, these should be stored in environment variables
    CREDENTIALS = {
        "username": os.getenv("TPA_USERNAME", "BVTPA"),
        "password": os.getenv("TPA_PASSWORD", "*d!n^+Cb@1"),
        "channel": os.getenv("TPA_CHANNEL", "LINE")
    }
    
    # Timeout settings (in seconds)
    TIMEOUTS = {
        "connect": int(os.getenv("TPA_CONNECT_TIMEOUT", "10")),
        "read": int(os.getenv("TPA_READ_TIMEOUT", "30")),
        "total": int(os.getenv("TPA_TOTAL_TIMEOUT", "45"))
    }
    
    # Token management settings
    TOKEN_SETTINGS = {
        "cache_key_prefix": "tpa_token",
        "expiry_seconds": int(os.getenv("TPA_TOKEN_EXPIRY", "3600")),  # 1 hour
        "refresh_threshold_seconds": int(os.getenv("TPA_TOKEN_REFRESH_THRESHOLD", "300"))  # 5 minutes
    }
    
    # Request retry settings
    RETRY_SETTINGS = {
        "max_retries": int(os.getenv("TPA_MAX_RETRIES", "3")),
        "backoff_factor": float(os.getenv("TPA_BACKOFF_FACTOR", "1.0")),
        "retry_on_status": [500, 502, 503, 504, 408, 429]
    }
    
    # Request headers
    DEFAULT_HEADERS = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "User-Agent": "Salmate-TPA-Client/1.0"
    }
    
    # API Response validation settings
    VALIDATION_SETTINGS = {
        "validate_ssl": bool(os.getenv("TPA_VALIDATE_SSL", "True").lower() == "true"),
        "max_response_size": int(os.getenv("TPA_MAX_RESPONSE_SIZE", "10485760")),  # 10MB
        "required_response_fields": {
            "get_token": ["token"],
            "search_citizen": ["Status", "CitizenID"],
            "policy_list": ["ListOfPolicyListSocial"],
            "policy_detail": ["PolicyDetail"]
        }
    }
    
    @classmethod
    def get_full_url(cls, endpoint_key: str) -> str:
        """
        Get the full URL for a specific endpoint
        
        Args:
            endpoint_key: Key from ENDPOINTS dict
            
        Returns:
            Full URL string
            
        Raises:
            KeyError: If endpoint_key is not found
        """
        if endpoint_key not in cls.ENDPOINTS:
            raise KeyError(f"Unknown endpoint: {endpoint_key}")
        
        return f"{cls.BASE_URL}{cls.ENDPOINTS[endpoint_key]}"
    
    @classmethod
    def get_request_config(cls) -> Dict[str, Any]:
        """
        Get complete request configuration
        
        Returns:
            Dictionary containing all request configuration
        """
        return {
            "base_url": cls.BASE_URL,
            "endpoints": cls.ENDPOINTS,
            "credentials": cls.CREDENTIALS,
            "timeouts": cls.TIMEOUTS,
            "headers": cls.DEFAULT_HEADERS,
            "retry_settings": cls.RETRY_SETTINGS,
            "validation": cls.VALIDATION_SETTINGS
        }
    
    @classmethod
    def validate_config(cls) -> bool:
        """
        Validate that all required configuration is present
        
        Returns:
            True if configuration is valid
            
        Raises:
            ValueError: If required configuration is missing
        """
        # Check required credentials
        required_creds = ["username", "password", "channel"]
        for cred in required_creds:
            if not cls.CREDENTIALS.get(cred):
                raise ValueError(f"Missing required credential: {cred}")
        
        # Check timeouts are positive
        for timeout_key, timeout_value in cls.TIMEOUTS.items():
            if timeout_value <= 0:
                raise ValueError(f"Invalid timeout value for {timeout_key}: {timeout_value}")
        
        # Check base URL is set
        if not cls.BASE_URL:
            raise ValueError("BASE_URL is not configured")
        
        return True


# Legacy configuration dictionary for backward compatibility
TPA_API_CONFIG = {
    "base_url": TPAAPIConfig.BASE_URL,
    "endpoints": TPAAPIConfig.ENDPOINTS,
    "credentials": TPAAPIConfig.CREDENTIALS,
    "timeouts": TPAAPIConfig.TIMEOUTS,
    "token_settings": TPAAPIConfig.TOKEN_SETTINGS,
    "retry_settings": TPAAPIConfig.RETRY_SETTINGS,
    "headers": TPAAPIConfig.DEFAULT_HEADERS,
    "validation": TPAAPIConfig.VALIDATION_SETTINGS
}


# Environment-specific configurations
ENVIRONMENT_CONFIGS = {
    "development": {
        "base_url": "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api",
        "validate_ssl": False,
        "debug_logging": True
    },
    "staging": {
        "base_url": "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api",
        "validate_ssl": True,
        "debug_logging": True
    },
    "production": {
        "base_url": "https://api.thirdpartyadmin.co.th/TPA.TMS.Web.API_PREPROV2/api",
        "validate_ssl": True,
        "debug_logging": False
    }
}


def get_environment_config(environment: str = None) -> Dict[str, Any]:
    """
    Get configuration for specific environment
    
    Args:
        environment: Environment name (development, staging, production)
                    If None, uses DJANGO_ENV environment variable
    
    Returns:
        Environment-specific configuration
    """
    if environment is None:
        environment = os.getenv("DJANGO_ENV", "development")
    
    env_config = ENVIRONMENT_CONFIGS.get(environment, ENVIRONMENT_CONFIGS["development"])
    
    # Merge with base configuration
    config = TPA_API_CONFIG.copy()
    config.update(env_config)
    
    return config
