# Generated by Django 4.2.24 on 2025-09-11 15:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customer', '0008_customerplatformidentity_current_line_rich_menu_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerPolicies',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('policy_no', models.CharField(help_text='Policy number from external TPA system', max_length=100)),
                ('policies', models.JSONField(help_text='Policy details and coverage information from TPA API')),
                ('claims', models.J<PERSON>NField(help_text='Claims data associated with this policy from TPA API')),
                ('updated_on', models.DateTimeField(auto_now=True, help_text='Last time this record was updated')),
                ('created_on', models.DateTimeField(auto_now_add=True, help_text='When this record was first created')),
                ('customer', models.ForeignKey(help_text='Customer who owns this policy', on_delete=django.db.models.deletion.CASCADE, related_name='policies', to='customer.customer')),
                ('updated_by', models.ForeignKey(help_text='User who last updated this record', on_delete=django.db.models.deletion.CASCADE, related_name='customer_policies_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Customer Policy',
                'verbose_name_plural': 'Customer Policies',
                'ordering': ['-updated_on'],
            },
        ),
        migrations.AddIndex(
            model_name='customerpolicies',
            index=models.Index(fields=['customer'], name='customer_cu_custome_b2c8b8_idx'),
        ),
        migrations.AddIndex(
            model_name='customerpolicies',
            index=models.Index(fields=['policy_no'], name='customer_cu_policy__b8c9a1_idx'),
        ),
        migrations.AddIndex(
            model_name='customerpolicies',
            index=models.Index(fields=['updated_on'], name='customer_cu_updated_4a5c2d_idx'),
        ),
        migrations.AddIndex(
            model_name='customerpolicies',
            index=models.Index(fields=['customer', 'policy_no'], name='customer_cu_custome_policy_8f3e1a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='customerpolicies',
            unique_together={('customer', 'policy_no')},
        ),
    ]
