# Insurance Policy and Claims Data Fetching - Implementation Plan

## 1. System Architecture Overview

### High-Level Architecture
```
Frontend (Svelte)           Backend (Django)              External APIs
┌─────────────────┐        ┌──────────────────┐         ┌─────────────────┐
│ PoliciesTab     │        │ Policy API       │         │ TPA API         │
│ - Fetch Button  │───────▶│ - Token Cache    │────────▶│ - GetToken      │
│ - Display Data  │        │ - Data Validation│         │ - <PERSON><PERSON><PERSON><PERSON> │
│ - Error Handling│◀───────│ - DB Operations  │         │ - PolicyList    │
└─────────────────┘        │ - Response Format│         │ - PolicyDetail  │
                           └──────────────────┘         └─────────────────┘
                                     │
                                     ▼
                           ┌───────────────────┐
                           │ Database          │
                           │ - CustomerPolicies│
                           │ - Customer        │
                           │ - User            │
                           └───────────────────┘
```

### Data Flow
1. User clicks fetch button → Frontend validates customer has Citizen ID
2. Frontend calls backend API → Backend retrieves/validates token from cache
3. Backend calls external APIs sequentially → Processes and validates responses
4. Backend checks existing records → Creates/updates CustomerPolicies table
5. Backend returns formatted data → Frontend displays in PoliciesTab

## 2. Database Schema

### CustomerPolicies Table
```sql
CREATE TABLE customer_customerpolicies (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER NOT NULL REFERENCES customer_customer(id),
    policy_no VARCHAR(100) NOT NULL,
    policies JSONB NOT NULL,
    claims JSONB NOT NULL,
    updated_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by INTEGER NOT NULL REFERENCES auth_user(id),
    created_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    CONSTRAINT unique_customer_policy UNIQUE(customer_id, policy_no)
);

-- Indexes for performance
CREATE INDEX idx_customer_policies_customer_id ON customer_customerpolicies(customer_id);
CREATE INDEX idx_customer_policies_policy_no ON customer_customerpolicies(policy_no);
CREATE INDEX idx_customer_policies_updated_on ON customer_customerpolicies(updated_on);
```

### JSON Schema for policies field
```json
{
  "policy_details": {
    "PolNo": "string",
    "MemberCode": "string",
    "Name": "string",
    "CitizenID": "string",
    "InsurerName": "string",
    "CompanyName": "string",
    "EffFrom": "string",
    "EffTo": "string",
    "PlanCode": "string",
    "PlanName": "string"
  },
  "coverage_details": [
    {
      "MainBenefit": "string",
      "Coverage": [
        {
          "CovNo": "string",
          "CovDesc": "string",
          "CovLimit": "string",
          "CovUtilized": "string"
        }
      ]
    }
  ]
}
```

### JSON Schema for claims field
```json
{
  "claims": [
    {
      "ClmNo": "string",
      "ClmPolNo": "string",
      "ClmStatus": "string",
      "ClmType": "string",
      "ClmDiagTH": "string",
      "ClmVisitDate": "string",
      "ClmIncurredAmt": "string",
      "ClmPayable": "string",
      "ClmProviderTH": "string"
    }
  ]
}
```

## 3. Backend Implementation Plan

### 3.1 API Endpoint Specifications

#### Primary Endpoint
```python
POST /api/customers/{customer_id}/fetch-policies-claims/
```

**Request Headers:**
- `Authorization: Bearer {token}`
- `Content-Type: application/json`

**Response Format:**
```json
{
  "success": true,
  "data": {
    "customer_id": 123,
    "policies_count": 2,
    "claims_count": 5,
    "policies": [...],
    "claims": [...],
    "last_updated": "2024-01-15T10:30:00Z"
  },
  "message": "Policies and claims fetched successfully"
}
```

#### Supporting Endpoints
```python
GET /api/customers/{customer_id}/policies-claims/  # Existing - retrieve stored data
GET /api/customers/{customer_id}/citizen-id/       # Check if customer has Citizen ID
```

### 3.2 External API Integration Workflow

#### Token Management Service
```python
# customer/_services/tpa_token_service.py
class TPATokenService:
    def __init__(self):
        self.cache_key_prefix = "tpa_token"
        self.token_expiry = 3600  # 1 hour
    
    def get_or_create_token(self, social_id, channel_id):
        # Check cache first
        # If expired/missing, call GetToken API
        # Store in cache with expiry
        pass
```

#### API Configuration
```python
# customer/_config/tpa_api_config.py
TPA_API_CONFIG = {
    "base_url": "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api",
    "endpoints": {
        "get_token": "/GetToken",
        "search_citizen": "/SearchCitizenID", 
        "policy_list": "/PolicyListSocial",
        "policy_detail": "/PolicyDetailSocial"
    },
    "credentials": {
        "username": "BVTPA",
        "password": "*d!n^+Cb@1",
        "channel": "LINE"
    },
    "timeouts": {
        "connect": 10,
        "read": 30
    }
}
```

### 3.3 Data Validation and Transformation

#### Response Validators
```python
# customer/_validators/tpa_response_validators.py
class TPAResponseValidator:
    @staticmethod
    def validate_token_response(response):
        # Validate token is string and not empty
        pass
    
    @staticmethod
    def validate_citizen_response(response):
        # Check Status = "1" and CitizenID exists
        pass
    
    @staticmethod
    def validate_policy_list_response(response):
        # Validate ListOfPolicyListSocial exists and has policies
        pass
```

#### Data Transformers
```python
# customer/_transformers/tpa_data_transformer.py
class TPADataTransformer:
    @staticmethod
    def transform_policy_data(raw_policy_data):
        # Transform external API response to internal format
        pass
    
    @staticmethod
    def transform_claims_data(raw_claims_data):
        # Transform claims data to internal format
        pass
```

### 3.4 Database Operations

#### CustomerPolicies Model
```python
# customer/models.py
class CustomerPolicies(models.Model):
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    policy_no = models.CharField(max_length=100)
    policies = models.JSONField()
    claims = models.JSONField()
    updated_on = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['customer', 'policy_no']
```

#### Service Layer
```python
# customer/_services/policy_claims_service.py
class PolicyClaimsService:
    def fetch_and_store_policies(self, customer_id, user_id):
        # 1. Validate customer has Citizen ID
        # 2. Get/create TPA token
        # 3. Verify Citizen ID with TPA
        # 4. Fetch policy list
        # 5. For each policy, fetch details
        # 6. Check existing records and update/create
        # 7. Return formatted response
        pass
    
    def update_or_create_policy_record(self, customer_id, policy_data, claims_data, user_id):
        # Check if record exists by policy_no
        # Compare data and update only if changed
        # Create new record if doesn't exist
        pass
```

## 4. Frontend Implementation Plan

### 4.1 PoliciesTab.svelte Updates

#### New Fetch Button Component
```svelte
<!-- Add to PoliciesTab.svelte -->
<div class="fetch-policies-section">
    <Button 
        color="primary" 
        on:click={fetchPoliciesFromAPI}
        disabled={fetchingFromAPI || !hasCitizenId}
    >
        {#if fetchingFromAPI}
            <Spinner size="4" class="mr-2" />
            Fetching...
        {:else}
            <RefreshOutline class="w-4 h-4 mr-2" />
            Fetch Latest Policies
        {/if}
    </Button>
    
    {#if !hasCitizenId}
        <p class="text-sm text-red-600 mt-2">
            Customer must have Citizen ID to fetch policies
        </p>
    {/if}
</div>
```

#### State Management
```javascript
// Add to PoliciesTab.svelte script
let fetchingFromAPI = false;
let hasCitizenId = false;
let fetchError = '';
let lastFetchTime = null;

// Check if customer has Citizen ID
async function checkCitizenId() {
    try {
        const result = await services.customers.checkCustomerCitizenId(
            customer.customer_id.toString(),
            access_token
        );
        hasCitizenId = result.has_citizen_id;
    } catch (error) {
        console.error('Error checking Citizen ID:', error);
        hasCitizenId = false;
    }
}

// Fetch policies from external API
async function fetchPoliciesFromAPI() {
    if (!hasCitizenId) {
        fetchError = 'Customer must have Citizen ID to fetch policies';
        return;
    }
    
    try {
        fetchingFromAPI = true;
        fetchError = '';
        
        const result = await services.customers.fetchPoliciesAndClaimsFromAPI(
            customer.customer_id.toString(),
            access_token
        );
        
        if (result.success) {
            // Refresh the displayed data
            await loadPoliciesData();
            lastFetchTime = new Date();
        } else {
            fetchError = result.error_msg || 'Failed to fetch policies';
        }
    } catch (error) {
        console.error('Error fetching policies from API:', error);
        fetchError = error.message || 'Failed to fetch policies from external API';
    } finally {
        fetchingFromAPI = false;
    }
}
```

### 4.2 API Service Integration

#### Update customers.service.ts
```typescript
// Add to src/lib/api/features/customer/customers.service.ts
async checkCustomerCitizenId(id: string, token: string): Promise<{has_citizen_id: boolean, res_status: number}> {
    try {
        const response = await fetch(`${this.baseUrl}/api/customers/${id}/citizen-id/`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ApiError('Failed to check Citizen ID', response.status);
        }

        const data = await response.json();
        return {
            has_citizen_id: data.has_citizen_id,
            res_status: response.status
        };
    } catch (error) {
        console.error('Error checking customer Citizen ID:', error);
        return {
            has_citizen_id: false,
            res_status: error.status || 500
        };
    }
}

async fetchPoliciesAndClaimsFromAPI(id: string, token: string): Promise<any> {
    try {
        const response = await fetch(`${this.baseUrl}/api/customers/${id}/fetch-policies-claims/`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new ApiError(
                errorData.message || 'Failed to fetch policies from API',
                response.status
            );
        }

        return await response.json();
    } catch (error) {
        console.error('Error fetching policies from external API:', error);
        throw error;
    }
}
```

### 4.3 UI/UX Considerations

#### Loading States
- Show spinner during API fetch
- Disable fetch button during operation
- Display progress indicators for multi-step process

#### Error Handling
- Clear error messages for different failure scenarios
- Retry mechanism for transient failures
- Graceful degradation when external API is unavailable

#### Data Display
- Highlight newly fetched data
- Show last fetch timestamp
- Indicate data freshness vs cached data

## 5. Implementation Sequence

### Phase 1: Backend Foundation (Week 1) - ✅ COMPLETED

#### 1. Create CustomerPolicies model and migration - ✅ COMPLETED

**Implementation Details:**
- Added `CustomerPolicies` model to `customer/models.py`
- Created Django migration `0009_customerpolicies.py`
- Implemented proper database indexes for performance

**Code Implementation:**
```python
class CustomerPolicies(models.Model):
    """
    Model to store insurance policies and claims data fetched from external TPA API.
    Each record represents a policy for a customer with associated claims data.
    """
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='policies')
    policy_no = models.CharField(max_length=100, help_text="Policy number from external TPA system")
    policies = models.JSONField(help_text="Policy details and coverage information from TPA API")
    claims = models.JSONField(help_text="Claims data associated with this policy from TPA API")
    updated_on = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='customer_policies_updated_by')
    created_on = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['customer', 'policy_no']
        indexes = [
            models.Index(fields=['customer']),
            models.Index(fields=['policy_no']),
            models.Index(fields=['updated_on']),
            models.Index(fields=['customer', 'policy_no']),
        ]
```

**Status:** ✅ Complete
**Files Created:**
- Updated: `customer/models.py` (added CustomerPolicies model)
- Created: `customer/migrations/0009_customerpolicies.py`

#### 2. Implement TPA API configuration - ✅ COMPLETED

**Implementation Details:**
- Created comprehensive configuration system with environment variable support
- Implemented both class-based and dictionary-based configuration patterns
- Added environment-specific configurations for development, staging, and production
- Included validation, retry settings, and security configurations

**Code Implementation:**
```python
class TPAAPIConfig:
    """Configuration class for TPA API integration"""

    BASE_URL = "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api"

    ENDPOINTS = {
        "get_token": "/GetToken",
        "search_citizen": "/SearchCitizenID",
        "policy_list": "/PolicyListSocial",
        "policy_detail": "/PolicyDetailSocial"
    }

    CREDENTIALS = {
        "username": os.getenv("TPA_USERNAME", "BVTPA"),
        "password": os.getenv("TPA_PASSWORD", "*d!n^+Cb@1"),
        "channel": os.getenv("TPA_CHANNEL", "LINE")
    }

    TOKEN_SETTINGS = {
        "cache_key_prefix": "tpa_token",
        "expiry_seconds": int(os.getenv("TPA_TOKEN_EXPIRY", "3600")),  # 1 hour
        "refresh_threshold_seconds": int(os.getenv("TPA_TOKEN_REFRESH_THRESHOLD", "300"))  # 5 minutes
    }
```

**Status:** ✅ Complete
**Files Created:**
- Created: `customer/_config/tpa_api_config.py`
- Created: `customer/_config/__init__.py`

#### 3. Create token management service - ✅ COMPLETED

**Implementation Details:**
- Built comprehensive Redis-based token caching system
- Implemented automatic token refresh before expiration
- Added thread-safe operations and comprehensive error handling
- Integrated with existing Redis patterns used in the codebase

**Code Implementation:**
```python
class TPATokenService:
    """Service for managing TPA API authentication tokens with Redis caching"""

    def __init__(self):
        self.redis_client = redis.Redis(
            host=getattr(settings, 'REDIS_HOST', 'redis'),
            port=getattr(settings, 'REDIS_PORT', 6379),
            db=getattr(settings, 'REDIS_TPA_TOKEN_DB', 4),  # Use separate DB for TPA tokens
            decode_responses=True
        )

    def get_or_create_token(self, social_id: str, channel_id: str) -> Optional[str]:
        """Get existing token from cache or create a new one"""
        # Try cache first, then call API if needed
        cached_token = self._get_token_from_cache(social_id, channel_id)
        if cached_token:
            return cached_token

        # Get new token and cache it
        token, success = self._call_token_api(social_id, channel_id)
        if success and token:
            self._store_token_in_cache(social_id, channel_id, token)
            return token

        return None
```

**Status:** ✅ Complete
**Files Created:**
- Created: `customer/_services/tpa_token_service.py`

#### 4. Build basic external API client - ✅ COMPLETED

**Implementation Details:**
- Created comprehensive API client with proper error handling
- Implemented structured response handling with TPAResponse dataclass
- Added retry logic, timeout handling, and response validation
- Integrated with token management service for automatic authentication

**Code Implementation:**
```python
class TPAAPIClient:
    """Client for interacting with TPA API"""

    def __init__(self):
        self.config = TPAAPIConfig()
        self.token_service = TPATokenService()
        self.session = requests.Session()

        # Configure retry strategy
        retry_strategy = Retry(
            total=self.config.RETRY_SETTINGS['max_retries'],
            backoff_factor=self.config.RETRY_SETTINGS['backoff_factor'],
            status_forcelist=self.config.RETRY_SETTINGS['retry_on_status']
        )

    def search_citizen_id(self, citizen_id: str, social_id: str, channel_id: str) -> TPAResponse:
        """Search for citizen ID in TPA system"""
        token = self.token_service.get_or_create_token(social_id, channel_id)
        if not token:
            return TPAResponse(success=False, error_message="Failed to obtain authentication token")

        payload = {"CitizenID": citizen_id, "Token": token}
        return self._make_request("POST", "search_citizen", payload)
```

**Status:** ✅ Complete
**Files Created:**
- Created: `customer/_services/tpa_api_client.py`
- Updated: `customer/_services/__init__.py`

**Phase 1 Summary:**
- ✅ All 4 tasks completed successfully
- ✅ Database model and migration ready
- ✅ Configuration system implemented with environment support
- ✅ Redis-based token management with 1-hour expiry
- ✅ Production-ready API client with comprehensive error handling
- ✅ Follows Django best practices and existing codebase patterns

**Next Steps:** Ready to proceed with Phase 2 - Core Backend Logic

### Phase 2: Core Backend Logic (Week 2) - ✅ COMPLETED

#### 1. Implement policy fetching workflow - ✅ COMPLETED

**Implementation Details:**
- Created comprehensive PolicyClaimsService class with complete workflow
- Integrated TPA API client, validators, and transformers
- Implemented error handling and logging throughout the workflow
- Added citizen ID validation and TPA verification steps

**Code Implementation:**
```python
class PolicyClaimsService:
    """Service for managing policy and claims data fetching and storage"""

    def fetch_and_store_policies(self, customer_id: int, user_id: int) -> Dict[str, Any]:
        """Complete workflow to fetch policies and claims from TPA API and store in database"""
        # 1. Validate customer has Citizen ID
        # 2. Get/create TPA token
        # 3. Verify Citizen ID with TPA
        # 4. Fetch policy list
        # 5. For each policy, fetch details
        # 6. Check existing records and update/create
        # 7. Return formatted response
```

**Status:** ✅ Complete
**Files Created:**
- Created: `customer/_services/policy_claims_service.py`

#### 2. Add data validation and transformation - ✅ COMPLETED

**Implementation Details:**
- Created TPAResponseValidator class for comprehensive API response validation
- Implemented TPADataTransformer class for converting external data to internal format
- Added validation for token, citizen, policy list, and policy detail responses
- Implemented data transformation with proper error handling and normalization

**Code Implementation:**
```python
class TPAResponseValidator:
    """Validator class for TPA API responses"""

    @staticmethod
    def validate_token_response(response: Dict[str, Any]) -> bool:
        # Validate token is string and not empty

    @staticmethod
    def validate_citizen_response(response: Dict[str, Any]) -> bool:
        # Check Status = "1" and CitizenID exists

    @staticmethod
    def validate_policy_list_response(response: Dict[str, Any]) -> bool:
        # Validate ListOfPolicyListSocial exists and has policies

class TPADataTransformer:
    """Transformer class for TPA API responses to internal format"""

    @staticmethod
    def transform_policy_data(raw_policy_data: Dict[str, Any]) -> Dict[str, Any]:
        # Transform external API response to internal format

    @staticmethod
    def transform_claims_data(raw_claims_data: Union[List[Dict[str, Any]], Dict[str, Any]]) -> Dict[str, Any]:
        # Transform claims data to internal format
```

**Status:** ✅ Complete
**Files Created:**
- Created: `customer/_validators/__init__.py`
- Created: `customer/_validators/tpa_response_validators.py`
- Created: `customer/_transformers/__init__.py`
- Created: `customer/_transformers/tpa_data_transformer.py`

#### 3. Create database operations (CRUD) - ✅ COMPLETED

**Implementation Details:**
- Implemented database operations in PolicyClaimsService
- Added update_or_create_policy_record method with transaction handling
- Implemented data comparison to avoid unnecessary updates
- Added comprehensive error handling and audit trail support

**Code Implementation:**
```python
def update_or_create_policy_record(
    self,
    customer_id: int,
    policy_no: str,
    policy_data: Dict[str, Any],
    claims_data: Dict[str, Any],
    user_id: int
) -> CustomerPolicies:
    """Update existing policy record or create new one"""
    # Check if record exists by policy_no
    # Compare data and update only if changed
    # Create new record if doesn't exist
```

**Status:** ✅ Complete
**Files Updated:**
- Updated: `customer/_services/policy_claims_service.py`

#### 4. Build main API endpoint - ✅ COMPLETED

**Implementation Details:**
- Created three new Django REST API endpoints
- Implemented proper authentication and permission handling
- Added comprehensive Swagger documentation
- Integrated with PolicyClaimsService for business logic

**API Endpoints Created:**
```python
# Check if customer has Citizen ID
GET /api/customers/{customer_id}/citizen-id/

# Fetch policies and claims from external TPA API
POST /api/customers/{customer_id}/fetch-policies-claims/

# Get stored policies and claims data
GET /api/customers/{customer_id}/stored-policies-claims/
```

**Code Implementation:**
```python
class CustomerCitizenIdCheckView(APIView):
    """API endpoint to check if a customer has a valid Citizen ID"""

class FetchPoliciesAndClaimsView(APIView):
    """API endpoint to fetch policies and claims from external TPA API"""

class CustomerStoredPoliciesAndClaimsView(APIView):
    """API endpoint to retrieve stored policies and claims data for a customer"""
```

**Status:** ✅ Complete
**Files Updated:**
- Updated: `customer/views.py` (added 3 new API views)
- Updated: `customer/urls.py` (added 3 new URL patterns)
- Updated: `customer/_services/__init__.py` (exported new service)

**Phase 2 Summary:**
- ✅ All 4 tasks completed successfully
- ✅ Complete policy fetching workflow implemented
- ✅ Comprehensive data validation and transformation system
- ✅ Database operations with proper transaction handling
- ✅ Three new REST API endpoints with full documentation
- ✅ Proper error handling and logging throughout
- ✅ Integration with existing Django patterns and authentication

**Next Steps:** Ready to proceed with Phase 3 - Frontend Integration

### Phase 3: Frontend Integration (Week 3)
1. Add Citizen ID check functionality
2. Implement fetch button and UI states
3. Update API service methods
4. Add error handling and user feedback

### Phase 4: Testing and Refinement (Week 4)
1. Unit tests for backend services
2. Integration tests for API workflow
3. Frontend component testing
4. End-to-end testing
5. Performance optimization

### Phase 5: Deployment and Monitoring (Week 5)
1. Production configuration
2. Monitoring and logging setup
3. Documentation updates
4. User training materials

## 6. Error Handling Strategy

### Backend Error Scenarios
1. **External API Failures**
   - Network timeouts → Retry with exponential backoff
   - Invalid credentials → Log and return authentication error
   - Rate limiting → Implement request queuing

2. **Data Validation Errors**
   - Invalid Citizen ID → Return user-friendly error
   - Missing required fields → Log and skip record
   - Malformed responses → Sanitize and continue

3. **Database Errors**
   - Constraint violations → Handle gracefully
   - Connection issues → Retry mechanism
   - Transaction failures → Rollback and retry

### Frontend Error Handling
1. **Network Errors**
   - Connection timeout → Show retry option
   - Server errors → Display generic error message
   - Authentication failures → Redirect to login

2. **User Input Errors**
   - Missing Citizen ID → Clear instruction message
   - Invalid permissions → Disable functionality

3. **Data Display Errors**
   - Empty responses → Show "No data available"
   - Malformed data → Show partial data with warnings

## 7. Testing Considerations

### Backend Testing
1. **Unit Tests**
   - Token management service
   - Data validators and transformers
   - Database operations
   - API response handling

2. **Integration Tests**
   - External API workflow
   - Database transactions
   - Error handling scenarios
   - Performance under load

3. **Mock Testing**
   - External API responses
   - Database failures
   - Network timeouts

### Frontend Testing
1. **Component Tests**
   - Fetch button functionality
   - Loading states
   - Error message display
   - Data rendering

2. **Integration Tests**
   - API service calls
   - State management
   - User interaction flows

3. **E2E Tests**
   - Complete fetch workflow
   - Error scenarios
   - Data persistence
   - UI responsiveness

### Test Data Requirements
1. **Mock Customer Data**
   - Customers with/without Citizen ID
   - Various platform identities
   - Different policy scenarios

2. **Mock API Responses**
   - Successful token generation
   - Valid/invalid Citizen ID responses
   - Policy lists with various structures
   - Claims data variations

3. **Error Scenarios**
   - API timeouts
   - Invalid responses
   - Authentication failures
   - Database constraints

## 8. Security Considerations

### API Security
- Store TPA credentials securely (environment variables)
- Implement request rate limiting
- Log all external API calls for audit
- Validate all external data before storage

### Data Protection
- Encrypt sensitive data in database
- Implement proper access controls
- Audit trail for data modifications
- GDPR compliance for customer data

### Frontend Security
- Validate user permissions before showing fetch button
- Sanitize displayed data
- Implement CSRF protection
- Secure token handling

## 9. Performance Optimization

### Backend Optimization
- Implement Redis caching for tokens
- Use connection pooling for external APIs
- Batch database operations where possible
- Implement async processing for large datasets

### Frontend Optimization
- Lazy load policy details
- Implement virtual scrolling for large lists
- Cache API responses appropriately
- Optimize re-rendering with proper state management

## 10. Monitoring and Logging

### Backend Monitoring
- Track external API response times
- Monitor token refresh rates
- Log data processing errors
- Alert on high failure rates

### Frontend Monitoring
- Track user interactions with fetch feature
- Monitor API call success rates
- Log client-side errors
- Performance metrics for data loading

This implementation plan provides a comprehensive roadmap for developing the insurance policy and claims data fetching feature while maintaining consistency with the existing codebase architecture and patterns.